"""
EasyLlama backend for Inferno - alternative to llama-cpp-python
"""

from typing import Dict, Any, List, Optional, Union, Generator
import time
import json

from .base import BaseBackend, BackendError

# Try to import easy-llama
try:
    import easy_llama as ez
    EASY_LLAMA_AVAILABLE = True
    EASY_LLAMA_ERROR = None
except ImportError as e:
    EASY_LLAMA_AVAILABLE = False
    EASY_LLAMA_ERROR = e
    ez = None


class EasyLlamaBackend(BaseBackend):
    """Backend implementation using easy-llama"""
    
    def __init__(self, model_name: str, model_path: str):
        super().__init__(model_name, model_path)
        self.llm: Optional[ez.Llama] = None
        
    @property
    def backend_name(self) -> str:
        return "easy-llama"
        
    @property
    def backend_version(self) -> str:
        if not EASY_LLAMA_AVAILABLE:
            return "not available"
        try:
            return getattr(ez, '__version__', 'unknown')
        except:
            return "unknown"
            
    def is_available(self) -> bool:
        return EASY_LLAMA_AVAILABLE
        
    def load_model(self, **kwargs) -> None:
        """Load model using easy-llama"""
        if not EASY_LLAMA_AVAILABLE:
            raise BackendError(f"easy-llama not available: {EASY_LLAMA_ERROR}")
            
        if self.is_loaded and self.llm is not None:
            # Check if we need to reload with different parameters
            n_ctx = kwargs.get('n_ctx')
            if n_ctx is not None and hasattr(self.llm, '_n_ctx') and self.llm._n_ctx != n_ctx:
                self.unload_model()
            else:
                return  # Already loaded with compatible parameters
        
        try:
            # Map parameters from llama-cpp-python style to easy-llama style
            params = {
                "path_model": self.model_path,
                "n_gpu_layers": kwargs.get("n_gpu_layers", 0),
                "n_ctx": kwargs.get("n_ctx", 4096),
                "n_threads": kwargs.get("n_threads", 0),
                "n_threads_batch": kwargs.get("n_threads_batch", 0),
                "verbose": kwargs.get("verbose", False),
                "vocab_only": kwargs.get("vocab_only", False),
                "warmup": kwargs.get("warmup", False),
                "offload_kqv": kwargs.get("offload_kqv", False),
                "flash_attn": kwargs.get("flash_attn", False),
            }
            
            # Remove None values and unsupported parameters
            params = {k: v for k, v in params.items() if v is not None}
            
            self.llm = ez.Llama(**params)
            self.is_loaded = True
            
        except Exception as e:
            raise BackendError(f"Failed to load model with easy-llama: {e}")
            
    def unload_model(self) -> None:
        """Unload the model"""
        if self.llm is not None:
            try:
                self.llm.free()
            except:
                pass
            self.llm = None
        self.is_loaded = False
        
    def _format_completion_response(self, text: str, finish_reason: str = "stop") -> Dict[str, Any]:
        """Format completion response in OpenAI format"""
        return {
            "id": f"cmpl-{int(time.time())}",
            "object": "text_completion",
            "created": int(time.time()),
            "model": self.model_name,
            "choices": [{
                "text": text,
                "index": 0,
                "logprobs": None,
                "finish_reason": finish_reason
            }],
            "usage": {
                "prompt_tokens": 0,  # easy-llama doesn't provide token counts
                "completion_tokens": 0,
                "total_tokens": 0
            }
        }
        
    def _format_chat_response(self, content: str, finish_reason: str = "stop") -> Dict[str, Any]:
        """Format chat response in OpenAI format"""
        return {
            "id": f"chatcmpl-{int(time.time())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": self.model_name,
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": content
                },
                "finish_reason": finish_reason
            }],
            "usage": {
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0
            }
        }
        
    def create_completion(
        self,
        prompt: str,
        max_tokens: Optional[int] = 16,
        temperature: float = 0.8,
        top_p: float = 0.95,
        min_p: float = 0.05,
        top_k: int = 40,
        repeat_penalty: float = 1.1,
        stream: bool = False,
        stop: Optional[List[str]] = None,
        seed: Optional[int] = None,
        suffix: Optional[str] = None,
        echo: bool = False,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        grammar: Optional[Any] = None,
        logit_bias: Optional[Dict[int, float]] = None,
    ) -> Union[Dict[str, Any], Generator[Dict[str, Any], None, None]]:
        """Create completion using easy-llama"""
        if not self.is_loaded or self.llm is None:
            raise BackendError("Model not loaded")
            
        try:
            # Tokenize the prompt
            prompt_tokens = self.llm.tokenize(prompt.encode('utf-8'), add_special=True, parse_special=False)
            
            # Create sampler preset
            sampler_preset = ez.SamplerPreset(
                temp=temperature,
                top_p=top_p,
                min_p=min_p,
                top_k=top_k,
                penalty_repeat=repeat_penalty,
                seed=seed
            )
            
            if stream:
                return self._stream_completion(prompt_tokens, max_tokens or 16, stop, sampler_preset, echo, prompt)
            else:
                # Generate tokens
                generated_tokens = self.llm.generate(
                    input_tokens=prompt_tokens,
                    n_predict=max_tokens or 16,
                    stop_tokens=self._convert_stop_strings_to_tokens(stop) if stop else None,
                    sampler_preset=sampler_preset
                )
                
                # Detokenize
                generated_text = self.llm.detokenize(generated_tokens, special=True)
                
                # Add echo if requested
                if echo:
                    full_text = prompt + generated_text
                else:
                    full_text = generated_text
                    
                return self._format_completion_response(full_text)
                
        except Exception as e:
            raise BackendError(f"Completion failed: {e}")
            
    def _stream_completion(self, prompt_tokens: List[int], max_tokens: int, stop: Optional[List[str]], 
                          sampler_preset, echo: bool, original_prompt: str) -> Generator[Dict[str, Any], None, None]:
        """Stream completion tokens"""
        try:
            stop_tokens = self._convert_stop_strings_to_tokens(stop) if stop else None
            
            if echo:
                # Yield the original prompt first
                yield {
                    "id": f"cmpl-{int(time.time())}",
                    "object": "text_completion",
                    "created": int(time.time()),
                    "model": self.model_name,
                    "choices": [{
                        "text": original_prompt,
                        "index": 0,
                        "logprobs": None,
                        "finish_reason": None
                    }]
                }
            
            # Stream generated tokens
            for token in self.llm.stream(
                input_tokens=prompt_tokens,
                n_predict=max_tokens,
                stop_tokens=stop_tokens,
                sampler_preset=sampler_preset
            ):
                token_text = self.llm.detokenize([token], special=True)
                
                yield {
                    "id": f"cmpl-{int(time.time())}",
                    "object": "text_completion",
                    "created": int(time.time()),
                    "model": self.model_name,
                    "choices": [{
                        "text": token_text,
                        "index": 0,
                        "logprobs": None,
                        "finish_reason": None
                    }]
                }
            
            # Final chunk
            yield {
                "id": f"cmpl-{int(time.time())}",
                "object": "text_completion",
                "created": int(time.time()),
                "model": self.model_name,
                "choices": [{
                    "text": "",
                    "index": 0,
                    "logprobs": None,
                    "finish_reason": "stop"
                }]
            }
            
        except Exception as e:
            raise BackendError(f"Streaming completion failed: {e}")
            
    def _convert_stop_strings_to_tokens(self, stop_strings: List[str]) -> List[int]:
        """Convert stop strings to token IDs"""
        if not stop_strings:
            return []
        
        stop_tokens = []
        for stop_str in stop_strings:
            try:
                tokens = self.llm.tokenize(stop_str.encode('utf-8'), add_special=False, parse_special=False)
                stop_tokens.extend(tokens)
            except:
                pass  # Skip invalid stop strings
        
        return stop_tokens
        
    def create_chat_completion(
        self,
        messages: List[Dict[str, Any]],
        max_tokens: Optional[int] = None,
        temperature: float = 0.7,
        top_p: float = 0.95,
        top_k: int = 40,
        min_p: float = 0.05,
        stream: bool = False,
        stop: Optional[List[str]] = None,
        seed: Optional[int] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: Optional[Union[str, Dict[str, Any]]] = None,
        format: Optional[Union[str, Dict[str, Any]]] = None,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        repeat_penalty: float = 1.1,
        logit_bias: Optional[Dict[int, float]] = None,
        grammar: Optional[Any] = None,
    ) -> Union[Dict[str, Any], Generator[Dict[str, Any], None, None]]:
        """Create chat completion using easy-llama"""
        if not self.is_loaded or self.llm is None:
            raise BackendError("Model not loaded")
            
        try:
            # Convert messages to prompt format
            prompt = self._messages_to_prompt(messages)
            
            # Use completion endpoint with chat formatting
            if stream:
                return self._stream_chat_completion(prompt, max_tokens, temperature, top_p, top_k, 
                                                  min_p, repeat_penalty, stop, seed)
            else:
                completion_response = self.create_completion(
                    prompt=prompt,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    top_p=top_p,
                    top_k=top_k,
                    min_p=min_p,
                    repeat_penalty=repeat_penalty,
                    stop=stop,
                    seed=seed,
                    stream=False
                )
                
                # Convert completion response to chat format
                content = completion_response["choices"][0]["text"]
                return self._format_chat_response(content)
                
        except Exception as e:
            raise BackendError(f"Chat completion failed: {e}")
            
    def _messages_to_prompt(self, messages: List[Dict[str, Any]]) -> str:
        """Convert chat messages to a prompt string"""
        prompt_parts = []
        
        for message in messages:
            role = message.get("role", "")
            content = message.get("content", "")
            
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"User: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
                
        prompt_parts.append("Assistant:")
        return "\n".join(prompt_parts)
        
    def _stream_chat_completion(self, prompt: str, max_tokens: Optional[int], temperature: float,
                               top_p: float, top_k: int, min_p: float, repeat_penalty: float,
                               stop: Optional[List[str]], seed: Optional[int]) -> Generator[Dict[str, Any], None, None]:
        """Stream chat completion"""
        for chunk in self.create_completion(
            prompt=prompt,
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
            min_p=min_p,
            repeat_penalty=repeat_penalty,
            stop=stop,
            seed=seed,
            stream=True
        ):
            # Convert completion chunk to chat format
            text = chunk["choices"][0]["text"]
            finish_reason = chunk["choices"][0]["finish_reason"]
            
            yield {
                "id": f"chatcmpl-{int(time.time())}",
                "object": "chat.completion.chunk",
                "created": int(time.time()),
                "model": self.model_name,
                "choices": [{
                    "index": 0,
                    "delta": {"content": text} if text else {},
                    "finish_reason": finish_reason
                }]
            }
            
    def create_embeddings(
        self,
        input: Union[str, List[str]],
        truncate: bool = True,
    ) -> Dict[str, Any]:
        """Create embeddings using easy-llama"""
        if not self.is_loaded or self.llm is None:
            raise BackendError("Model not loaded")
            
        # easy-llama doesn't have built-in embedding support
        # This is a placeholder implementation
        raise BackendError("Embeddings not supported by easy-llama backend")
