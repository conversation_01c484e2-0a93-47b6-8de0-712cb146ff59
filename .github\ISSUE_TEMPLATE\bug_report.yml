name: Bug Report
description: File a bug report
title: "[BUG]: "
labels: ["bug", "triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!
  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen?
      placeholder: Tell us what you see!
    validations:
      required: true
  - type: textarea
    id: reproduction-steps
    attributes:
      label: Steps to reproduce
      description: How can we reproduce this issue?
      placeholder: |
        1. Run `inferno serve modelname`
        2. Send a request to the API endpoint...
        3. See error...
    validations:
      required: true
  - type: dropdown
    id: version
    attributes:
      label: Version
      description: What version of Inferno are you running?
      options:
        - Latest from main branch
        - 0.1.0 (please specify if other in the text box below)
    validations:
      required: true
  - type: dropdown
    id: os
    attributes:
      label: What operating system are you using?
      options:
        - Windows
        - macOS
        - Linux
        - Other
    validations:
      required: true
  - type: input
    id: python-version
    attributes:
      label: Python version
      description: What version of Python are you running?
      placeholder: e.g., Python 3.9.12
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Relevant log output
      description: Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks.
      render: shell
  - type: textarea
    id: additional
    attributes:
      label: Additional information
      description: Add any other context about the problem here.