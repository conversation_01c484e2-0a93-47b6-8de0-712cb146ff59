"""
OpenAI-compatible HTTP backend for Inferno.

This backend talks to an OpenAI-compatible API (OpenAI, OpenRouter, Together,
Ollama's /v1 endpoints, llama.cpp server, etc.). It mirrors the small subset of
interfaces used by inferno.core.llm.LLMInterface so it can be used as a
transparent fallback when llama-cpp-python is not installed.
"""
from __future__ import annotations

import json
import os
import urllib.request
import urllib.error
from typing import Any, Dict, Generator, List, Optional, Union

from ..utils.config import config


def _get_api_base() -> str:
    # Priority: env -> config -> sensible default (local OpenAI-compatible server)
    return (
        os.environ.get("OPENAI_API_BASE")
        or str(config.get("openai_api_base", "http://127.0.0.1:11434/v1"))
    )


def _get_api_key() -> Optional[str]:
    return os.environ.get("OPENAI_API_KEY") or config.get("openai_api_key", None)


class OpenAIBackend:
    """
    Minimal OpenAI-compatible client with the subset we need.
    Implements:
      - create_completion
      - create_chat_completion
      - create_embeddings
    """

    def __init__(self, model_name: str, api_base: Optional[str] = None, api_key: Optional[str] = None) -> None:
        self.model_name = model_name
        self.api_base = (api_base or _get_api_base()).rstrip("/")
        self.api_key = api_key if api_key is not None else _get_api_key()

    # The local interface expects a load_model() method; this is a no-op here.
    def load_model(self, **_: Any) -> None:  # type: ignore[no-redef]
        return None

    def _request(self, path: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        url = f"{self.api_base}{path}"
        data = json.dumps(payload).encode("utf-8")
        headers = {"Content-Type": "application/json"}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        req = urllib.request.Request(url, data=data, headers=headers, method="POST")
        try:
            with urllib.request.urlopen(req) as resp:
                resp_text = resp.read().decode("utf-8")
                return json.loads(resp_text)
        except urllib.error.HTTPError as e:
            try:
                err_body = e.read().decode("utf-8")
            except Exception:
                err_body = str(e)
            raise RuntimeError(f"OpenAIBackend HTTP {e.code}: {err_body}") from None
        except urllib.error.URLError as e:
            raise RuntimeError(f"OpenAIBackend connection error: {e}") from None

    def create_completion(
        self,
        prompt: str,
        max_tokens: Optional[int] = 16,
        temperature: float = 0.8,
        top_p: float = 0.95,
        min_p: float = 0.05,  # not standard; ignored if remote doesn't support
        top_k: int = 40,      # not standard; ignored if remote doesn't support
        repeat_penalty: float = 1.1,  # not standard; ignored
        stream: bool = False,
        stop: Optional[List[str]] = None,
        seed: Optional[int] = None,
        suffix: Optional[str] = None,
        echo: bool = False,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        grammar: Optional[Any] = None,
        logit_bias: Optional[Dict[int, float]] = None,
    ) -> Union[Dict[str, Any], Generator[Dict[str, Any], None, None]]:
        payload: Dict[str, Any] = {
            "model": self.model_name,
            "prompt": prompt,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "stop": stop or [],
            "echo": echo,
        }
        if frequency_penalty:
            payload["frequency_penalty"] = frequency_penalty
        if presence_penalty:
            payload["presence_penalty"] = presence_penalty
        if suffix is not None:
            payload["suffix"] = suffix
        if seed is not None:
            payload["seed"] = seed
        if logit_bias:
            payload["logit_bias"] = logit_bias
        # Non-standard params forwarded via extra dict for compat servers (e.g., llama.cpp/ollama)
        extra: Dict[str, Any] = {}
        if min_p is not None:
            extra["min_p"] = min_p
        if top_k is not None:
            extra["top_k"] = top_k
        if repeat_penalty is not None:
            extra["repeat_penalty"] = repeat_penalty
        if grammar is not None:
            extra["grammar"] = grammar
        if extra:
            payload["extra"] = extra

        # Streaming not implemented in this fallback (routes call us with stream=False)
        if stream:
            raise NotImplementedError("OpenAIBackend streaming is not implemented in this fallback.")

        return self._request("/v1/completions", payload)

    def create_chat_completion(
        self,
        messages: List[Dict[str, Any]],
        max_tokens: Optional[int] = None,
        temperature: float = 0.7,
        top_p: float = 0.95,
        top_k: int = 40,      # non-standard
        min_p: float = 0.05,  # non-standard
        stream: bool = False,
        stop: Optional[List[str]] = None,
        seed: Optional[int] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: Optional[Union[str, Dict[str, Any]]] = None,
        format: Optional[Union[str, Dict[str, Any]]] = None,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        repeat_penalty: float = 1.1,  # non-standard
        logit_bias: Optional[Dict[int, float]] = None,
        grammar: Optional[Any] = None,
    ) -> Union[Dict[str, Any], Generator[Dict[str, Any], None, None]]:
        payload: Dict[str, Any] = {
            "model": self.model_name,
            "messages": messages,
            "temperature": temperature,
            "top_p": top_p,
        }
        if max_tokens is not None:
            payload["max_tokens"] = max_tokens
        if stop:
            payload["stop"] = stop
        if tools:
            payload["tools"] = tools
        if tool_choice:
            payload["tool_choice"] = tool_choice
        if isinstance(format, dict):
            payload["response_format"] = format
        elif isinstance(format, str):
            payload["response_format"] = {"type": format}
        if frequency_penalty:
            payload["frequency_penalty"] = frequency_penalty
        if presence_penalty:
            payload["presence_penalty"] = presence_penalty
        if logit_bias:
            payload["logit_bias"] = logit_bias
        extra: Dict[str, Any] = {}
        if seed is not None:
            extra["seed"] = seed
        if top_k is not None:
            extra["top_k"] = top_k
        if min_p is not None:
            extra["min_p"] = min_p
        if repeat_penalty is not None:
            extra["repeat_penalty"] = repeat_penalty
        if grammar is not None:
            extra["grammar"] = grammar
        if extra:
            payload["extra"] = extra

        if stream:
            raise NotImplementedError("OpenAIBackend streaming is not implemented in this fallback.")

        return self._request("/v1/chat/completions", payload)

    def create_embeddings(
        self,
        input: Union[str, List[str]],
        truncate: bool = True,
    ) -> Dict[str, Any]:
        payload: Dict[str, Any] = {
            "model": self.model_name,
            "input": input,
        }
        # Some servers accept additional fields via extra
        payload["extra"] = {"truncate": truncate}
        return self._request("/v1/embeddings", payload)

